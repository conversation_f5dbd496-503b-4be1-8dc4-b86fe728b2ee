# -*- coding: utf-8 -*-
"""
工具函数模块 - 通用工具和辅助功能
===================================

这个模块提供了项目中使用的各种通用工具函数，包括:
- 彩色控制台输出功能
- 自定义异常类定义
- 系统依赖检查功能
- 外部工具验证功能

这些工具函数被项目的各个模块广泛使用，提供了统一的
输出格式、错误处理和环境检查功能。

主要功能:
- 彩色日志输出: 支持不同级别的彩色控制台输出
- 异常处理: 自定义异常类，便于错误分类和处理
- 依赖检查: 验证Python包和外部工具的可用性
- 调试支持: 可控制的调试信息输出

作者: nt_dl_team
版本: v2.2.0
"""

import subprocess


class LogColors:
    """
    控制台颜色常量类
    ==================

    定义了用于控制台彩色输出的ANSI转义序列常量。
    这些颜色用于区分不同级别的日志信息，提升用户体验。

    颜色说明:
    - SUCCESS (绿色): 成功操作的提示信息
    - INFO (蓝色): 一般信息和状态提示
    - WARN (黄色): 警告信息，需要注意但不影响运行
    - ERROR (红色): 错误信息，表示操作失败
    - BLINK (闪烁): 特殊强调效果
    - DEBUG (灰色): 调试信息，通常较为详细
    - RESET: 重置颜色，恢复默认显示
    """

    SUCCESS = "\033[92m"  # 绿色 - 成功信息
    INFO = "\033[94m"  # 蓝色 - 一般信息
    WARN = "\033[93m"  # 黄色 - 警告信息
    ERROR = "\033[91m"  # 红色 - 错误信息
    BLINK = "\033[5m"  # 闪烁效果 - 特殊强调
    DEBUG = "\033[90m"  # 灰色 - 调试信息
    RESET = "\033[0m"  # 重置颜色 - 恢复默认


class ProcessingError(Exception):
    """
    自定义处理异常类
    ==================

    用于项目中统一的错误处理和异常管理。
    继承自Python标准的Exception类，添加了自定义的错误消息处理。

    这个异常类主要用于:
    - URL解析错误
    - 文件处理错误
    - 网络请求错误
    - 数据处理错误

    使用方式:
        raise ProcessingError("具体的错误描述信息")

    Attributes:
        message (str): 错误消息内容
    """

    def __init__(self, message: str):
        """
        初始化处理异常

        Args:
            message (str): 错误描述信息
        """
        self.message = message
        super().__init__(self.message)


# ==================== 彩色输出函数 ====================


def print_success(message: str) -> None:
    """
    打印成功信息（绿色）

    用于显示操作成功的提示信息，如下载完成、验证通过等。

    Args:
        message (str): 要显示的成功信息

    Example:
        print_success("✅ 文件下载完成")
    """
    print(f"{LogColors.SUCCESS}{message}{LogColors.RESET}")


def print_info(message: str) -> None:
    """
    打印一般信息（蓝色）

    用于显示程序运行状态、进度信息等一般性提示。

    Args:
        message (str): 要显示的信息内容

    Example:
        print_info("🔍 正在解析URL...")
    """
    print(f"{LogColors.INFO}{message}{LogColors.RESET}")


def print_warn(message: str) -> None:
    """
    打印警告信息（黄色）

    用于显示需要用户注意但不影响程序继续运行的警告信息。

    Args:
        message (str): 要显示的警告信息

    Example:
        print_warn("⚠️ 配置文件存在问题，使用默认设置")
    """
    print(f"{LogColors.WARN}{message}{LogColors.RESET}")


def print_error(message: str) -> None:
    """
    打印错误信息（红色）

    用于显示错误信息，通常表示操作失败或遇到严重问题。

    Args:
        message (str): 要显示的错误信息

    Example:
        print_error("❌ 网络连接失败")
    """
    print(f"{LogColors.ERROR}{message}{LogColors.RESET}")


def print_debug(message: str) -> None:
    """
    打印调试信息（灰色，仅在调试模式下显示）

    用于显示详细的调试信息，只有在设置了DEBUG环境变量时才会输出。
    这样可以在开发和调试时获得详细信息，而在正常使用时保持界面简洁。

    调试模式启用方法:
    - 设置环境变量: export DEBUG=1
    - 或者: export DEBUG=true
    - 或者: export DEBUG=yes

    Args:
        message (str): 要显示的调试信息

    Example:
        print_debug("浏览器启动参数: --headless --no-sandbox")
    """
    import os

    # 检查DEBUG环境变量，支持多种格式的真值
    if os.getenv("DEBUG", "").lower() in ("1", "true", "yes"):
        print(f"{LogColors.DEBUG}[DEBUG] {message}{LogColors.RESET}")


# ==================== 系统检查函数 ====================


def check_ffmpeg() -> bool:
    """
    检查FFmpeg是否可用
    ====================

    验证系统中是否安装了FFmpeg工具，这是音频处理的必需组件。
    FFmpeg用于音频格式转换、元数据嵌入等功能。

    检查方法:
    1. 尝试执行 'ffmpeg -version' 命令
    2. 检查命令的返回码是否为0（成功）
    3. 设置10秒超时，避免长时间等待

    Returns:
        bool: True表示FFmpeg可用，False表示不可用或未安装

    Note:
        如果FFmpeg不可用，程序将无法进行音频处理，
        用户需要先安装FFmpeg才能正常使用下载功能。
    """
    try:
        # 执行FFmpeg版本检查命令
        result = subprocess.run(
            ["ffmpeg", "-version"],  # 执行的命令
            capture_output=True,  # 捕获输出，避免显示在控制台
            text=True,  # 以文本模式处理输出
            timeout=10,  # 10秒超时限制
        )
        # 检查命令是否成功执行（返回码为0）
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        # 捕获各种可能的异常:
        # - TimeoutExpired: 命令执行超时
        # - FileNotFoundError: 找不到ffmpeg命令
        # - SubprocessError: 其他子进程相关错误
        return False


def check_dependencies() -> bool:
    """
    检查所有必要的Python依赖包
    ==============================

    验证项目运行所需的所有Python第三方库是否已正确安装。
    这个函数会逐一检查每个依赖包，并收集缺失的包名。

    检查的依赖包:
    - requests: HTTP请求库，用于网络通信
    - beautifulsoup4: HTML解析库，用于网页内容解析
    - pycryptodome: 加密库，用于网易云API的加密算法
    - playwright: 浏览器自动化库，用于模拟浏览器下载

    Returns:
        bool: True表示所有依赖都已安装，False表示有缺失的依赖

    Side Effects:
        如果有缺失的依赖，会打印错误信息和安装命令提示
    """
    missing_deps = []  # 存储缺失的依赖包名

    # 检查requests库（HTTP请求）
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    # 检查BeautifulSoup库（HTML解析）
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        missing_deps.append("beautifulsoup4")

    # 检查PyCryptodome库（加密功能）
    try:
        from Crypto.Cipher import AES
    except ImportError:
        missing_deps.append("pycryptodome")

    # 检查Playwright库（浏览器自动化）
    try:
        from playwright.async_api import async_playwright
    except ImportError:
        missing_deps.append("playwright")

    # 如果有缺失的依赖，显示错误信息和安装提示
    if missing_deps:
        print_error("错误：缺少关键依赖库。请使用以下命令安装：")
        print_error(f"pip install {' '.join(missing_deps)}")
        return False

    return True
