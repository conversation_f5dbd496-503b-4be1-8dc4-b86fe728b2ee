# -*- coding: utf-8 -*-
"""
日志记录模块
提供详细的日志记录和调试功能
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from .utils import LogColors


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    COLORS = {
        "DEBUG": LogColors.DEBUG,
        "INFO": LogColors.INFO,
        "WARNING": LogColors.WARN,
        "ERROR": LogColors.ERROR,
        "CRITICAL": LogColors.ERROR + LogColors.BLINK,
    }

    def format(self, record):
        # 添加颜色
        color = self.COLORS.get(record.levelname, "")
        record.levelname = f"{color}{record.levelname}{LogColors.RESET}"
        record.msg = f"{color}{record.msg}{LogColors.RESET}"

        return super().format(record)


class Logger:
    """日志管理器"""

    def __init__(self, name: str = "nt_dl", log_dir: Optional[Path] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers(log_dir)

    def _setup_handlers(self, log_dir: Optional[Path]) -> None:
        """设置日志处理器"""
        # 控制台处理器（彩色输出）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter("%(asctime)s - %(levelname)s - %(message)s", datefmt="%H:%M:%S")
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 文件处理器（详细日志）
        if log_dir:
            log_dir_path = Path(log_dir)
            log_dir_path.mkdir(exist_ok=True)
            log_file = log_dir_path / f"nt_dl_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

    def debug(self, message: str) -> None:
        """调试日志"""
        self.logger.debug(message)

    def info(self, message: str) -> None:
        """信息日志"""
        self.logger.info(message)

    def warning(self, message: str) -> None:
        """警告日志"""
        self.logger.warning(message)

    def error(self, message: str) -> None:
        """错误日志"""
        self.logger.error(message)

    def critical(self, message: str) -> None:
        """严重错误日志"""
        self.logger.critical(message)

    def log_download_start(self, url: str) -> None:
        """记录下载开始"""
        self.info(f"开始下载: {url}")

    def log_download_success(self, url: str, file_size: int, duration: float) -> None:
        """记录下载成功"""
        speed = (file_size / 1024 / 1024) / duration if duration > 0 else 0
        self.info(f"下载成功: {url} ({file_size/1024/1024:.1f}MB, {duration:.1f}s, {speed:.1f}MB/s)")

    def log_download_failure(self, url: str, error: str) -> None:
        """记录下载失败"""
        self.error(f"下载失败: {url} - {error}")

    def log_environment_setup(self, music_dir: Path, temp_dir: Path, browser_dir: Path) -> None:
        """记录环境设置"""
        self.info("环境设置完成:")
        self.info(f"  音乐目录: {music_dir}")
        self.info(f"  临时目录: {temp_dir}")
        self.info(f"  浏览器数据: {browser_dir}")

    def log_cleanup(self, cleaned_files: int, freed_space: int) -> None:
        """记录清理操作"""
        self.info(f"清理完成: 删除{cleaned_files}个文件，释放{freed_space/1024/1024:.1f}MB空间")


# 创建默认日志器
def get_logger(log_dir: Optional[Path] = None) -> Logger:
    """获取日志器实例"""
    return Logger(log_dir=log_dir)
