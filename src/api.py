# -*- coding: utf-8 -*-
"""
API模块
处理网易云音乐API请求和URL解析
"""

import json
import re
from typing import List

import requests
from bs4 import BeautifulSoup

from .config import HEADERS
from .crypto_secure import get_weapi_params
from .utils import print_info, print_success, print_warn, ProcessingError


def parse_and_route_url(raw_url: str) -> List[str]:
    """
    解析输入URL，判断是单曲、专辑还是歌单，并返回一个歌曲URL列表

    Args:
        raw_url: 原始URL

    Returns:
        歌曲URL列表

    Raises:
        ProcessingError: URL解析失败
    """
    cleaned_url = raw_url.strip().split("&")[0]
    print_info(f"正在解析URL: {cleaned_url}")

    # 匹配歌单或专辑
    playlist_album_match = re.search(r"music\.163\.com/(?:#/)?(album|playlist)\?id=(\d+)", cleaned_url)
    if playlist_album_match:
        url_type, item_id = playlist_album_match.groups()
        try:
            if url_type == "playlist":
                return fetch_playlist_data(item_id)
            elif url_type == "album":
                return fetch_album_data(item_id)
        except Exception as e:
            raise ProcessingError(f"无法从 {url_type} ID {item_id} 获取歌曲列表: {e}")

    # 匹配单曲
    song_match = re.search(r"music\.163\.com/(?:#/)?song\?id=(\d+)", cleaned_url)
    if song_match:
        song_id = song_match.groups()[0]
        print_info("✓ 识别为单曲URL")
        return [f"https://music.163.com/song?id={song_id}"]

    raise ProcessingError(f"URL格式不匹配任何有效的单曲、专辑或歌单: {raw_url}")


def fetch_playlist_data(playlist_id: str) -> List[str]:
    """
    获取歌单数据

    Args:
        playlist_id: 歌单ID

    Returns:
        歌曲URL列表
    """
    print_info("✓ 识别为歌单，正在获取歌曲列表...")

    api_url = "https://music.163.com/weapi/v6/playlist/detail?csrf_token="
    payload = {"id": playlist_id, "n": 100000, "s": 8, "csrf_token": ""}

    encrypted_data = get_weapi_params(payload)

    try:
        response = requests.post(api_url, headers=HEADERS, data=encrypted_data, timeout=10)
        response.raise_for_status()
        result = response.json()

        playlist_name = result.get("playlist", {}).get("name", "Unknown_Playlist")
        track_ids = result.get("playlist", {}).get("trackIds", [])

        if not track_ids:
            print_warn("警告: 未在歌单中找到任何歌曲")
            return []

        song_ids = [str(item["id"]) for item in track_ids]
        print_success(f"成功: 在歌单《{playlist_name}》中找到 {len(song_ids)} 首歌曲")

        return [f"https://music.163.com/song?id={song_id}" for song_id in song_ids]

    except requests.RequestException as e:
        raise ProcessingError(f"获取歌单数据失败: {e}")
    except (KeyError, ValueError) as e:
        raise ProcessingError(f"解析歌单数据失败: {e}")


def fetch_album_data(album_id: str) -> List[str]:
    """
    获取专辑数据

    Args:
        album_id: 专辑ID

    Returns:
        歌曲URL列表
    """
    print_info("✓ 识别为专辑，正在获取歌曲列表...")

    album_url = f"https://music.163.com/album?id={album_id}"

    try:
        response = requests.get(album_url, headers=HEADERS, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, "html.parser")

        # 获取专辑名称
        album_name_elem = soup.find("h2", class_="f-ff2")
        album_name = album_name_elem.text.strip() if album_name_elem else "Unknown_Album"

        # 获取歌曲列表
        textarea = soup.find("textarea", id="song-list-pre-data")
        if not textarea or not textarea.string:
            raise ProcessingError("无法在专辑页面中找到歌曲列表")

        tracks = json.loads(textarea.string)
        song_ids = [str(track["id"]) for track in tracks]

        print_success(f"成功: 在专辑《{album_name}》中找到 {len(song_ids)} 首歌曲")

        return [f"https://music.163.com/song?id={song_id}" for song_id in song_ids]

    except requests.RequestException as e:
        raise ProcessingError(f"获取专辑数据失败: {e}")
    except (json.JSONDecodeError, KeyError, ValueError) as e:
        raise ProcessingError(f"解析专辑数据失败: {e}")
