# -*- coding: utf-8 -*-
"""
输入验证模块 - 安全输入验证和数据清理
===================================

本模块提供全面的输入验证、数据清理和安全检查功能。
采用白名单验证策略和多层防护机制，防止各类注入攻击。

主要功能:
- URL格式和安全性验证
- 文件路径安全检查
- 文件名清理和规范化
- 用户输入参数验证
- 数据类型和边界检查

安全特性:
- 白名单域名验证
- 路径遍历攻击防护
- 文件名注入防护
- 参数类型强制检查
- 边界值安全验证

作者: nt_dl_team
版本: v2.3.0

⚠️ 安全声明:
本模块中的所有验证规则均基于OWASP安全指导原则和业界最佳实践。
- 验证策略: 采用白名单验证和多层防护
- 安全标准: 遵循OWASP Top 10安全指南
- 防护机制: 基于纵深防御安全理念
- 使用限制: 仅供技术学习和安全研究
- 责任声明: 验证效果由用户自行评估和承担风险
"""

import os
import re
from pathlib import Path
from typing import Union, List, Dict, Any
from urllib.parse import urlparse, parse_qs

from .logging_utils import get_logger

logger = get_logger(__name__)


class InputValidator:
    """
    输入验证器

    提供全面的输入验证和安全检查功能。
    采用白名单策略和多层验证机制。
    """

    # 允许的域名白名单
    ALLOWED_DOMAINS = {
        "music.163.com",
        "api.toubiec.cn",
        "localhost",  # 开发环境
        "127.0.0.1",  # 本地测试
    }

    # 危险文件名字符
    DANGEROUS_FILENAME_CHARS = '<>:"/\\|?*\x00'

    # 保留文件名（Windows系统）
    RESERVED_FILENAMES = {
        "CON",
        "PRN",
        "AUX",
        "NUL",
        "COM1",
        "COM2",
        "COM3",
        "COM4",
        "COM5",
        "COM6",
        "COM7",
        "COM8",
        "COM9",
        "LPT1",
        "LPT2",
        "LPT3",
        "LPT4",
        "LPT5",
        "LPT6",
        "LPT7",
        "LPT8",
        "LPT9",
    }

    @classmethod
    def validate_url(cls, url: str, strict: bool = True) -> bool:
        """
        验证URL格式和安全性

        Args:
            url: 要验证的URL
            strict: 是否启用严格模式验证

        Returns:
            True if URL有效且安全, False otherwise
        """
        if not url or not isinstance(url, str):
            logger.warning("URL为空或类型错误")
            return False

        # 清理URL
        url = url.strip()

        # 基础格式验证
        try:
            parsed = urlparse(url)
        except Exception as e:
            logger.warning(f"URL解析失败: {e}")
            return False

        # 检查必要组件
        if not all([parsed.scheme, parsed.netloc]):
            logger.warning("URL缺少必要组件（scheme或netloc）")
            return False

        # 协议验证
        if parsed.scheme.lower() not in ["http", "https"]:
            logger.warning(f"不支持的协议: {parsed.scheme}")
            return False

        # 域名白名单检查
        if strict:
            domain = parsed.netloc.lower()
            # 移除端口号
            if ":" in domain:
                domain = domain.split(":")[0]

            if not any(domain == allowed or domain.endswith("." + allowed) for allowed in cls.ALLOWED_DOMAINS):
                logger.warning(f"域名不在白名单中: {domain}")
                return False

        # 路径安全检查
        if parsed.path:
            # 防止路径遍历
            if ".." in parsed.path or "//" in parsed.path:
                logger.warning("检测到路径遍历攻击尝试")
                return False

            # 检查危险路径模式
            dangerous_patterns = [
                r"\.\./",  # 相对路径
                r"/\.\.",  # 上级目录
                r"%2e%2e",  # URL编码的..
                r"%2f",  # URL编码的/
                r"%5c",  # URL编码的\
            ]

            path_lower = parsed.path.lower()
            for pattern in dangerous_patterns:
                if re.search(pattern, path_lower):
                    logger.warning(f"检测到危险路径模式: {pattern}")
                    return False

        # 查询参数验证
        if parsed.query:
            try:
                query_params = parse_qs(parsed.query)
                # 检查参数值长度
                for key, values in query_params.items():
                    for value in values:
                        if len(value) > 1000:  # 限制参数值长度
                            logger.warning(f"查询参数值过长: {key}")
                            return False
            except Exception as e:
                logger.warning(f"查询参数解析失败: {e}")
                return False

        logger.info(f"URL验证通过: {url}")
        return True

    @classmethod
    def validate_netease_url(cls, url: str) -> bool:
        """
        验证网易云音乐URL格式

        Args:
            url: 网易云音乐URL

        Returns:
            True if URL格式正确, False otherwise
        """
        if not cls.validate_url(url):
            return False

        # 网易云音乐URL模式
        netease_patterns = [
            r"music\.163\.com/(?:#/)?song\?id=\d+",  # 单曲
            r"music\.163\.com/(?:#/)?playlist\?id=\d+",  # 歌单
            r"music\.163\.com/(?:#/)?album\?id=\d+",  # 专辑
        ]

        for pattern in netease_patterns:
            if re.search(pattern, url):
                logger.info(f"网易云音乐URL验证通过: {url}")
                return True

        logger.warning(f"不支持的网易云音乐URL格式: {url}")
        return False

    @classmethod
    def validate_file_path(cls, path: Union[str, Path], check_exists: bool = False) -> bool:
        """
        验证文件路径安全性

        Args:
            path: 文件路径
            check_exists: 是否检查文件存在性

        Returns:
            True if 路径安全, False otherwise
        """
        if not path:
            logger.warning("文件路径为空")
            return False

        try:
            path_obj = Path(path).resolve()
        except Exception as e:
            logger.warning(f"路径解析失败: {e}")
            return False

        # 检查路径是否在允许的目录下
        try:
            cwd = Path.cwd().resolve()
            # 确保路径在当前工作目录或其子目录下
            path_obj.relative_to(cwd)
        except ValueError:
            logger.warning(f"路径超出允许范围: {path_obj}")
            return False

        # 检查路径组件
        for part in path_obj.parts:
            if part in ["..", "."]:
                logger.warning(f"路径包含危险组件: {part}")
                return False

        # 检查文件存在性（可选）
        if check_exists and not path_obj.exists():
            logger.warning(f"文件不存在: {path_obj}")
            return False

        logger.info(f"文件路径验证通过: {path_obj}")
        return True

    @classmethod
    def sanitize_filename(cls, filename: str, max_length: int = 255) -> str:
        """
        清理文件名，移除危险字符

        Args:
            filename: 原始文件名
            max_length: 最大文件名长度

        Returns:
            清理后的安全文件名
        """
        if not filename or not isinstance(filename, str):
            logger.warning("文件名为空或类型错误")
            return "unnamed_file"

        # 移除危险字符
        cleaned = filename
        for char in cls.DANGEROUS_FILENAME_CHARS:
            cleaned = cleaned.replace(char, "_")

        # 移除控制字符
        cleaned = "".join(char for char in cleaned if ord(char) >= 32)

        # 处理保留文件名
        name_without_ext = os.path.splitext(cleaned)[0].upper()
        if name_without_ext in cls.RESERVED_FILENAMES:
            cleaned = f"_{cleaned}"

        # 移除首尾空格和点号
        cleaned = cleaned.strip(" .")

        # 确保文件名不为空
        if not cleaned:
            cleaned = "unnamed_file"

        # 限制文件名长度
        if len(cleaned) > max_length:
            name, ext = os.path.splitext(cleaned)
            max_name_length = max_length - len(ext)
            cleaned = name[:max_name_length] + ext

        logger.info(f"文件名清理完成: {filename} -> {cleaned}")
        return cleaned

    @classmethod
    def validate_audio_quality(cls, quality: str) -> bool:
        """
        验证音频质量参数

        Args:
            quality: 音频质量字符串

        Returns:
            True if 质量参数有效, False otherwise
        """
        valid_qualities = {
            "standard",  # 标准音质
            "higher",  # 较高音质
            "exhigh",  # 极高音质
            "lossless",  # 无损音质
            "hires",  # Hi-Res音质
        }

        if quality and quality.lower() in valid_qualities:
            logger.info(f"音频质量验证通过: {quality}")
            return True

        logger.warning(f"无效的音频质量参数: {quality}")
        return False

    @classmethod
    def validate_download_path(cls, download_path: str) -> bool:
        """
        验证下载路径

        Args:
            download_path: 下载目录路径

        Returns:
            True if 路径有效, False otherwise
        """
        if not download_path:
            logger.warning("下载路径为空")
            return False

        try:
            path_obj = Path(download_path)

            # 检查路径安全性
            if not cls.validate_file_path(path_obj):
                return False

            # 检查是否为目录
            if path_obj.exists() and not path_obj.is_dir():
                logger.warning(f"下载路径不是目录: {path_obj}")
                return False

            # 检查写入权限
            if path_obj.exists():
                if not os.access(path_obj, os.W_OK):
                    logger.warning(f"下载路径无写入权限: {path_obj}")
                    return False
            else:
                # 检查父目录权限
                parent = path_obj.parent
                if not parent.exists() or not os.access(parent, os.W_OK):
                    logger.warning(f"下载路径父目录无写入权限: {parent}")
                    return False

            logger.info(f"下载路径验证通过: {path_obj}")
            return True

        except Exception as e:
            logger.warning(f"下载路径验证失败: {e}")
            return False

    @classmethod
    def validate_config_dict(cls, config: Dict[str, Any]) -> bool:
        """
        验证配置字典

        Args:
            config: 配置字典

        Returns:
            True if 配置有效, False otherwise
        """
        if not isinstance(config, dict):
            logger.warning("配置不是字典类型")
            return False

        # 检查配置键名
        for key in config.keys():
            if not isinstance(key, str):
                logger.warning(f"配置键名不是字符串: {key}")
                return False

            # 检查键名长度
            if len(key) > 100:
                logger.warning(f"配置键名过长: {key}")
                return False

        # 检查配置值
        for key, value in config.items():
            # 检查值的类型和大小
            if isinstance(value, str) and len(value) > 10000:
                logger.warning(f"配置值过长: {key}")
                return False
            elif isinstance(value, (list, dict)) and len(str(value)) > 50000:
                logger.warning(f"配置值过大: {key}")
                return False

        logger.info("配置字典验证通过")
        return True


class SecurityChecker:
    """
    安全检查器

    提供系统级安全检查和威胁检测功能。
    """

    @staticmethod
    def check_system_security() -> Dict[str, bool]:
        """
        检查系统安全状态

        Returns:
            安全检查结果字典
        """
        results = {}

        # 检查文件权限
        try:
            test_file = Path("test_permission.tmp")
            test_file.write_text("test")
            test_file.unlink()
            results["file_permission"] = True
        except Exception:
            results["file_permission"] = False

        # 检查网络连接
        try:
            import socket

            socket.create_connection(("8.8.8.8", 53), timeout=3)
            results["network_access"] = True
        except Exception:
            results["network_access"] = False

        # 检查依赖库
        try:
            import requests
            import playwright
            import cryptography

            results["dependencies"] = True
        except ImportError:
            results["dependencies"] = False

        logger.info(f"系统安全检查完成: {results}")
        return results

    @staticmethod
    def detect_suspicious_activity(activity_log: List[str]) -> List[str]:
        """
        检测可疑活动

        Args:
            activity_log: 活动日志列表

        Returns:
            可疑活动列表
        """
        suspicious_patterns = [
            r"\.\./",  # 路径遍历
            r"<script",  # XSS攻击
            r"union.*select",  # SQL注入
            r"exec\(",  # 代码执行
            r"eval\(",  # 代码评估
        ]

        suspicious_activities = []

        for log_entry in activity_log:
            for pattern in suspicious_patterns:
                if re.search(pattern, log_entry, re.IGNORECASE):
                    suspicious_activities.append(log_entry)
                    logger.warning(f"检测到可疑活动: {log_entry}")
                    break

        return suspicious_activities


# 全局验证器实例
validator = InputValidator()
security_checker = SecurityChecker()
