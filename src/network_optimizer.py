# -*- coding: utf-8 -*-
"""
网络优化模块
提供智能的网络连接优化和管理功能
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Dict
import aiohttp
from urllib.parse import urlparse
from .logging_utils import get_logger


@dataclass
class NetworkStats:
    """网络统计信息"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_bytes: int = 0
    total_time: float = 0.0
    avg_speed: float = 0.0

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

    def update_speed(self):
        """更新平均速度"""
        if self.total_time > 0:
            self.avg_speed = (self.total_bytes / 1024 / 1024) / self.total_time


class NetworkOptimizer:
    """网络优化器"""

    def __init__(self):
        self.logger = get_logger()
        self.stats = NetworkStats()
        self.connection_pool = None
        self.session_cache: Dict[str, aiohttp.ClientSession] = {}

        # 网络配置
        self.max_connections = 10
        self.connection_timeout = 30
        self.read_timeout = 60
        self.max_retries = 3

        # 速度限制
        self.rate_limit_delay = 0.1  # 请求间隔
        self.last_request_time = 0.0

    async def create_optimized_session(self, domain: str = None) -> aiohttp.ClientSession:
        """创建优化的HTTP会话"""
        if domain and domain in self.session_cache:
            return self.session_cache[domain]

        # 连接器配置
        connector = aiohttp.TCPConnector(
            limit=self.max_connections,
            limit_per_host=5,
            ttl_dns_cache=300,  # DNS缓存5分钟
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True,
        )

        # 超时配置
        timeout = aiohttp.ClientTimeout(
            total=self.connection_timeout + self.read_timeout,
            connect=self.connection_timeout,
            sock_read=self.read_timeout,
        )

        # 创建会话
        session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            },
        )

        if domain:
            self.session_cache[domain] = session

        self.logger.debug(f"创建优化会话: {domain or 'default'}")
        return session

    async def optimized_request(self, method: str, url: str, **kwargs) -> aiohttp.ClientResponse:
        """优化的HTTP请求"""
        domain = urlparse(url).netloc
        session = await self.create_optimized_session(domain)

        # 速度限制
        await self._apply_rate_limit()

        start_time = time.time()
        self.stats.total_requests += 1

        try:
            async with session.request(method, url, **kwargs) as response:
                # 记录统计信息
                duration = time.time() - start_time
                self.stats.total_time += duration
                self.stats.successful_requests += 1

                # 如果有内容长度，记录字节数
                if response.headers.get("Content-Length"):
                    self.stats.total_bytes += int(response.headers["Content-Length"])

                self.stats.update_speed()

                self.logger.debug(f"请求成功: {method} {url} " f"({response.status}, {duration:.2f}s)")

                return response

        except Exception as e:
            self.stats.failed_requests += 1
            self.logger.warning(f"请求失败: {method} {url} - {e}")
            raise

    async def _apply_rate_limit(self):
        """应用速度限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def download_with_progress(
        self, url: str, file_path: str, chunk_size: int = 8192, progress_callback=None
    ) -> bool:
        """带进度的下载"""
        try:
            session = await self.create_optimized_session()

            async with session.get(url) as response:
                if response.status != 200:
                    self.logger.error(f"下载失败: HTTP {response.status}")
                    return False

                total_size = int(response.headers.get("Content-Length", 0))
                downloaded = 0

                with open(file_path, "wb") as f:
                    async for chunk in response.content.iter_chunked(chunk_size):
                        f.write(chunk)
                        downloaded += len(chunk)

                        if progress_callback and total_size > 0:
                            progress = downloaded / total_size
                            progress_callback(progress, downloaded, total_size)

                self.logger.info(f"下载完成: {file_path} ({downloaded} bytes)")
                return True

        except Exception as e:
            self.logger.error(f"下载异常: {e}")
            return False

    def get_network_stats(self) -> Dict:
        """获取网络统计信息"""
        return {
            "total_requests": self.stats.total_requests,
            "successful_requests": self.stats.successful_requests,
            "failed_requests": self.stats.failed_requests,
            "success_rate": self.stats.success_rate,
            "total_bytes_mb": self.stats.total_bytes / 1024 / 1024,
            "total_time": self.stats.total_time,
            "avg_speed_mbps": self.stats.avg_speed,
            "active_sessions": len(self.session_cache),
        }

    async def cleanup(self):
        """清理资源"""
        for session in self.session_cache.values():
            await session.close()
        self.session_cache.clear()
        self.logger.debug("网络优化器资源已清理")

    def optimize_for_speed(self):
        """优化速度设置"""
        self.max_connections = 20
        self.rate_limit_delay = 0.05
        self.connection_timeout = 15
        self.logger.info("已切换到速度优化模式")

    def optimize_for_stability(self):
        """优化稳定性设置"""
        self.max_connections = 5
        self.rate_limit_delay = 0.5
        self.connection_timeout = 60
        self.logger.info("已切换到稳定性优化模式")


# 全局网络优化器实例
network_optimizer = NetworkOptimizer()
