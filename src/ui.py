# -*- coding: utf-8 -*-
"""
用户界面模块
负责用户交互、进度显示、结果展示等
"""

import re
from typing import Dict

from .utils import print_error, print_info, print_success, print_warn


def display_program_header(music_dir_path: str, browser_data_dir_path: str) -> None:
    """
    显示程序头部信息

    Args:
        music_dir_path: 音乐目录路径
        browser_data_dir_path: 浏览器数据目录路径
    """
    print("=" * 70)
    print_success("🎵 网易云音乐统一下载器 - 模块化版本")
    print("集中管理 | 隐藏文件夹 | 自动清理 | 优化性能")
    print_info(f"📁 音乐保存位置: {music_dir_path}")
    print_info(f"🔒 浏览器数据位置: {browser_data_dir_path} (隐藏)")
    print("=" * 70)


def select_audio_quality() -> str:
    """
    选择音频质量

    Returns:
        选择的音质字符串
    """
    print("\n" + "=" * 50)
    print("🎵 音质选择 - 推荐设置")
    print("=" * 50)
    print("1: 超清母带 (SVIP) - 体积最大，需要SVIP会员")
    print("2: 高解析度无损 (VIP) - 🌟 强烈推荐 🌟")
    print("   • 音质标准：接近CD品质，满足绝大多数听音需求")
    print("   • 文件体积：相比超清母带节省60-70%存储空间")
    print("   • 兼容性强：支持更多设备和播放器")
    print("   • 成功率高：解析站点支持度更好")
    print("=" * 50)

    while True:
        choice = input("请选择音质 (直接回车默认选择推荐的选项2): ").strip()

        if choice == "" or choice == "2":
            print_success("✓ 已选择: 高解析度无损 (VIP) - 推荐选择！")
            return "高解析度无损(VIP)"
        elif choice == "1":
            print_info("✓ 已选择: 超清母带 (SVIP)")
            print_warn("注意: 如果解析失败会自动降级到高解析度无损")
            return "超清母带(SVIP)"
        else:
            print_warn("输入无效，请输入 1 或 2，或直接回车选择推荐选项")


def create_lightweight_task_indicator(url: str, current: int, total: int) -> str:
    """
    创建轻量级任务指示器

    Args:
        url: 歌曲URL
        current: 当前任务编号
        total: 总任务数

    Returns:
        任务指示器字符串
    """
    song_id = extract_song_id_from_url(url) or "Unknown"
    return f"🎵 [{current}/{total}] 歌曲ID: {song_id}"


def extract_song_id_from_url(url: str) -> str:
    """
    从URL中提取歌曲ID

    Args:
        url: 歌曲URL

    Returns:
        歌曲ID
    """
    match = re.search(r"song\?id=(\d+)", url)
    return match.group(1) if match else "Unknown"


def display_final_results(total_urls: list, failed_urls: Dict[str, str], music_dir_path: str) -> None:
    """
    显示最终结果

    Args:
        total_urls: 总URL列表
        failed_urls: 失败的URL字典
        music_dir_path: 音乐目录路径
    """
    success_count = len(total_urls) - len(failed_urls)

    print("\n" + "=" * 25 + " ✅ 全部处理完成 " + "=" * 25)
    print_info(f"📊 总计: {len(total_urls)} 首歌曲")
    print_success(f"✅ 成功: {success_count} 首")

    if failed_urls:
        print_error(f"❌ 失败: {len(failed_urls)} 首")
        print("\n--- 失败的URL汇总 ---")
        for i, (url, reason) in enumerate(failed_urls.items(), 1):
            print(f"{i}. URL: {url}")
            print(f"   错误: {reason}")
    else:
        print_success("🎉 所有URL均已成功处理！")

    print("\n" + "=" * 25 + " 📁 文件位置信息 " + "=" * 25)
    print_info(f"🎵 音乐文件保存在: {music_dir_path}")

    if success_count > 0:
        print_success("✨ 下载的音频文件已包含:")
        print_success("   • 封面图片（嵌入到音频文件中）")
        print_success("   • 歌词信息（嵌入到metadata中）")
        print_success("   • 完整的标签信息")

    print("\n" + "=" * 25 + " ⚡ 架构优化 " + "=" * 25)
    print_info("🔧 采用模块化架构，代码结构清晰")
    print_info("📊 智能进度监控，减少系统开销")
    print_info("🛡️  基于稳定的Rust架构移植，确保可靠性")
    print_info("🗂️  集中管理：所有程序文件都在下载目录下")
    print_info("🔒 隐藏browser_data目录，避免用户混淆")
    print_info("🧹 自动清理：启动和退出时智能清理遗留文件")

    print("=" * 70)
