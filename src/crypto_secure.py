# -*- coding: utf-8 -*-
"""
安全加密模块 - 网易云音乐API加密算法实现（安全版本）
=====================================================

这个模块实现了网易云音乐weapi接口所需的完整加密算法，使用现代安全的加密库。
网易云音乐使用双层加密机制来保护API请求数据：
1. 第一层：使用固定nonce进行AES加密
2. 第二层：使用随机密钥进行AES加密
3. 第三层：使用RSA公钥加密随机密钥

安全改进:
- 使用 cryptography 库替代 pycrypto
- 采用安全的随机数生成器
- 完善的错误处理和验证
- 内存安全的密钥处理

作者: nt_dl_team
版本: v2.3.0
基于: 网易云音乐官方API加密协议（安全加固版）

⚠️ 加密声明:
本模块中的所有加密实现均基于公开的密码学标准和网易云官方协议。
- 加密算法: 基于公开的AES和RSA标准
- 协议实现: 遵循网易云官方API文档
- 安全标准: 采用现代密码学最佳实践
- 使用限制: 仅供技术学习和协议研究
- 责任声明: 加密实现风险由用户自行承担
"""

import json
import secrets
from base64 import b64encode
from typing import Dict

from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend

from .config import MODULUS, NONCE, PUBKEY
from .logging_utils import get_logger
from .utils import print_error

logger = get_logger(__name__)


class SecureAESCipher:
    """
    安全的AES加密器

    使用 cryptography 库实现的安全AES加密，
    支持CBC模式和PKCS7填充。
    """

    def __init__(self, key: bytes, iv: bytes):
        """
        初始化AES加密器

        Args:
            key: AES密钥（16字节）
            iv: 初始化向量（16字节）
        """
        self.key = key
        self.iv = iv

        # 创建AES算法实例
        algorithm = algorithms.AES(key)
        mode = modes.CBC(iv)
        self.cipher = Cipher(algorithm, mode, backend=default_backend())

    def encrypt(self, data: bytes) -> bytes:
        """
        加密数据

        Args:
            data: 要加密的数据

        Returns:
            加密后的数据
        """
        # 创建加密器
        encryptor = self.cipher.encryptor()

        # 添加PKCS7填充
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data)
        padded_data += padder.finalize()

        # 执行加密
        ciphertext = encryptor.update(padded_data)
        ciphertext += encryptor.finalize()

        return ciphertext


def generate_secure_random_key(length: int = 16) -> str:
    """
    生成安全的随机密钥

    使用密码学安全的随机数生成器生成密钥。

    Args:
        length: 密钥长度，默认16位

    Returns:
        生成的随机密钥字符串
    """
    # 定义密钥字符集：大小写字母 + 数字
    alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

    # 使用密码学安全的随机数生成器
    return "".join(secrets.choice(alphabet) for _ in range(length))


def secure_aes_encrypt(data: str, key: str) -> str:
    """
    安全的AES加密函数

    使用cryptography库实现的安全AES-128-CBC加密。

    Args:
        data: 要加密的原始数据
        key: AES加密密钥

    Returns:
        Base64编码的加密结果

    Raises:
        ValueError: 加密失败
    """
    try:
        # 固定的初始化向量，网易云API的标准配置
        iv = b"0102030405060708"

        # 确保密钥长度正确
        key_bytes = key.encode("utf-8")
        if len(key_bytes) != 16:
            # 如果密钥长度不足16字节，进行填充
            key_bytes = key_bytes.ljust(16, b"\0")[:16]

        # 创建AES加密器
        aes_cipher = SecureAESCipher(key_bytes, iv)

        # 加密数据
        data_bytes = data.encode("utf-8")
        encrypted_bytes = aes_cipher.encrypt(data_bytes)

        # Base64编码
        return b64encode(encrypted_bytes).decode("utf-8")

    except Exception as e:
        logger.error(f"AES加密失败: {e}")
        raise ValueError(f"AES加密失败: {e}")


def secure_rsa_encrypt(text: str, pub_key: str, modulus: str) -> str:
    """
    安全的RSA加密函数

    使用大整数运算实现RSA加密，遵循网易云的特殊协议。

    Args:
        text: 要加密的文本（通常是AES密钥）
        pub_key: RSA公钥指数（十六进制）
        modulus: RSA模数（十六进制）

    Returns:
        256位十六进制加密结果

    Raises:
        ValueError: 加密失败
    """
    try:
        # 验证输入参数
        if not all([text, pub_key, modulus]):
            raise ValueError("RSA加密参数不能为空")

        # 将文本转换为UTF-8字节并反转（网易云特殊要求）
        text_bytes = text.encode("utf-8")
        text_reversed = text_bytes[::-1]

        # 将字节转换为整数
        text_int = int.from_bytes(text_reversed, byteorder="big")

        # 转换RSA参数
        try:
            pub_key_int = int(pub_key, 16)
            modulus_int = int(modulus, 16)
        except ValueError as e:
            raise ValueError(f"RSA参数格式错误: {e}")

        # 验证参数范围
        if text_int >= modulus_int:
            raise ValueError("文本数值超出模数范围")

        # 执行RSA数学运算: (text^e) mod n
        encrypted_int = pow(text_int, pub_key_int, modulus_int)

        # 格式化为256位十六进制字符串
        encrypted_hex = format(encrypted_int, "x").zfill(256)

        return encrypted_hex

    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        raise ValueError(f"RSA加密失败: {e}")


def get_secure_weapi_params(payload: dict) -> Dict[str, str]:
    """
    获取安全的weapi加密参数

    这是模块的主要接口函数，实现完整的网易云weapi加密流程。
    使用安全的加密库和最佳实践。

    Args:
        payload: 要发送给API的原始数据字典

    Returns:
        包含加密参数的字典

    Raises:
        ValueError: 加密失败
    """
    try:
        # 验证输入
        if not isinstance(payload, dict):
            raise ValueError("payload必须是字典类型")

        # 将字典转换为JSON字符串
        try:
            payload_str = json.dumps(payload, ensure_ascii=False, separators=(",", ":"))
        except (TypeError, ValueError) as e:
            raise ValueError(f"JSON序列化失败: {e}")

        # 生成安全的随机密钥
        secret_key = generate_secure_random_key(16)
        logger.info(f"生成随机密钥，长度: {len(secret_key)}")

        # 第一次AES加密 - 使用固定nonce
        try:
            params = secure_aes_encrypt(payload_str, NONCE)
        except ValueError as e:
            raise ValueError(f"第一次AES加密失败: {e}")

        # 第二次AES加密 - 使用随机密钥
        try:
            params = secure_aes_encrypt(params, secret_key)
        except ValueError as e:
            raise ValueError(f"第二次AES加密失败: {e}")

        # RSA加密随机密钥
        try:
            enc_sec_key = secure_rsa_encrypt(secret_key, PUBKEY, MODULUS)
        except ValueError as e:
            raise ValueError(f"RSA加密失败: {e}")

        # 验证结果
        if not params or not enc_sec_key:
            raise ValueError("加密结果为空")

        if len(enc_sec_key) != 256:
            raise ValueError(f"RSA加密结果长度错误: {len(enc_sec_key)}")

        logger.info("weapi参数加密成功")

        return {"params": params, "encSecKey": enc_sec_key}

    except Exception as e:
        logger.error(f"weapi参数加密失败: {e}")
        print_error(f"加密失败: {e}")
        raise


def validate_encryption_config() -> bool:
    """
    验证加密配置

    检查加密所需的配置参数是否正确。

    Returns:
        True if 配置有效, False otherwise
    """
    try:
        # 检查必要的配置常量
        if not MODULUS or not NONCE or not PUBKEY:
            logger.error("加密配置常量缺失")
            return False

        # 验证MODULUS格式
        try:
            int(MODULUS, 16)
        except ValueError:
            logger.error("MODULUS格式错误")
            return False

        # 验证PUBKEY格式
        try:
            int(PUBKEY, 16)
        except ValueError:
            logger.error("PUBKEY格式错误")
            return False

        # 验证NONCE长度
        if len(NONCE) != 16:
            logger.error(f"NONCE长度错误: {len(NONCE)}")
            return False

        logger.info("加密配置验证通过")
        return True

    except Exception as e:
        logger.error(f"加密配置验证失败: {e}")
        return False


# 向后兼容的别名
get_weapi_params = get_secure_weapi_params


# 模块初始化时验证配置
if not validate_encryption_config():
    logger.warning("加密配置验证失败，可能影响功能正常使用")
