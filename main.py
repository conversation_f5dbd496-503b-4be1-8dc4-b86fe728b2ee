#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nt-dl-py - 网易云音乐下载器主程序入口
==================================

这是nt-dl-py网易云音乐下载器的主程序入口文件，负责协调各个模块的工作流程。
程序采用异步架构设计，支持单曲、歌单、专辑的批量下载。

主要功能:
- 环境初始化和配置验证
- 用户交互和URL解析
- 下载流程控制和错误处理
- 临时文件清理和性能统计
- 优雅的程序退出和资源清理

技术特性:
- 完整的PyInstaller打包支持
- 智能的浏览器环境检测
- 结构化的日志记录系统
- 全面的错误处理和恢复

作者: nt_dl_team
版本: v2.2.0
基于: Rust版本的完整功能迁移

⚠️ 重要声明:
本项目仅供编程学习和技术研究使用。
- 数据来源: 公开互联网和第三方公益解析服务
- 技术实现: 基于公开的网络协议和技术文档
- 使用限制: 仅限个人学习，禁止商业用途
- 责任声明: 开发者不承担任何使用风险和法律责任
- 合规要求: 用户应遵守当地法律法规和版权规定
"""

import asyncio
import sys

# 导入核心功能模块
from src.api import parse_and_route_url  # URL解析和路由
from src.config_validator import validate_config  # 配置验证
from src.downloader import execute_lightweight_download_workflow  # 下载工作流
from src.env_setup import env_manager  # 环境管理器
from src.file_handler import handle_temp_directory_cleanup  # 文件处理
from src.logging_utils import get_logger  # 日志系统
from src.performance import performance_monitor  # 性能监控
from src.pyinstaller_compat import setup_pyinstaller_environment  # 打包兼容
from src.ui import display_final_results, display_program_header, select_audio_quality  # 用户界面
from src.utils import check_dependencies, check_ffmpeg, print_error, print_info, print_warn  # 工具函数


async def main():
    """
    主程序异步函数
    ================

    这是程序的核心控制流程，按以下步骤执行:
    1. 环境检测和初始化
    2. 依赖验证和配置检查
    3. 用户交互和URL解析
    4. 下载流程执行
    5. 结果统计和清理工作

    Returns:
        int: 程序退出码
            0 - 正常退出
            1 - 发生错误

    Raises:
        Exception: 各种运行时异常，会被上层捕获并处理
    """
    # ==================== 第一阶段: 环境初始化 ====================

    # 设置PyInstaller环境（必须在导入playwright之前执行）
    # 这个步骤会检测是否为打包环境，并设置相应的浏览器路径
    setup_pyinstaller_environment()

    # 初始化日志记录系统
    # 如果音乐目录已设置，则在其下创建.logs子目录存储日志
    logger = get_logger(env_manager.music_dir / ".logs" if env_manager.music_dir else None)

    # ==================== 第二阶段: 配置和依赖验证 ====================

    # 验证程序配置的完整性和正确性
    # 包括常量值、URL格式、加密参数等的验证
    if not validate_config():
        print_warn("⚠️ 配置验证发现问题，但程序将继续运行")

    # 检查所有必需的Python依赖包
    # 包括requests、beautifulsoup4、pycryptodome、playwright等
    if not check_dependencies():
        logger.error("依赖检查失败")
        return 1

    # ==================== 第三阶段: 环境初始化 ====================

    # 初始化工作环境，包括目录创建、权限检查等
    try:
        env_manager.initialize_environment()
        # 记录环境设置信息到日志
        logger.log_environment_setup(
            env_manager.music_dir,  # 音乐下载目录
            env_manager.temp_dir,  # 临时文件目录
            env_manager.current_browser_data_dir,  # 浏览器数据目录
        )
    except Exception as e:
        print_error(f"环境初始化失败: {e}")
        logger.error(f"环境初始化失败: {e}")
        return 1

    # 显示程序欢迎信息和配置概览
    display_program_header(
        str(env_manager.music_dir), str(env_manager.current_browser_data_dir)  # 音乐保存路径  # 浏览器数据路径
    )

    # ==================== 第四阶段: 外部工具检查 ====================

    # 检查FFmpeg是否可用（音频处理必需）
    # FFmpeg用于音频格式转换和元数据嵌入
    if not check_ffmpeg():
        print_error("❌ 严重错误: 未安装FFmpeg或未在系统PATH中找到")
        print_info("💡 在macOS上，您可以通过Homebrew安装: brew install ffmpeg")
        logger.error("FFmpeg检查失败")
        return 1

    # ==================== 第五阶段: 用户交互 ====================

    # 获取用户输入的网易云音乐URL
    # 支持单曲、歌单、专辑等多种URL格式
    raw_url = input("🔗 请输入网易云音乐URL: ").strip()
    if not raw_url:
        print_warn("未输入URL，程序退出")
        logger.info("用户未输入URL，程序退出")
        return 0

    # 记录用户输入到日志（用于调试和统计）
    logger.info(f"用户输入URL: {raw_url}")

    # ==================== 第六阶段: URL解析 ====================

    # 解析用户输入的URL，获取具体的歌曲列表
    # 这个过程会调用网易云API获取歌单/专辑的详细信息
    try:
        urls_to_process = parse_and_route_url(raw_url)
        logger.info(f"URL解析成功，获得 {len(urls_to_process)} 个歌曲URL")
    except Exception as e:
        print_error(f"❌ URL解析失败: {e}")
        logger.error(f"URL解析失败: {e}")
        return 1

    # 验证是否获取到有效的歌曲URL
    if not urls_to_process:
        print_error("❌ 未能获取到有效的URL，程序退出")
        logger.error("未获得有效的URL")
        return 1

    # ==================== 第七阶段: 音质选择 ====================

    # 让用户选择下载音质
    # 支持超清母带(SVIP)和高解析度无损(VIP)两种音质
    selected_quality = select_audio_quality()
    logger.info(f"用户选择音质: {selected_quality}")

    # ==================== 第八阶段: 执行下载 ====================

    # 显示下载开始信息
    print_info(f"\n开始处理 {len(urls_to_process)} 个URL")
    logger.info(f"开始下载流程，共 {len(urls_to_process)} 个URL")

    # 执行轻量级下载工作流
    # 这是核心的下载逻辑，使用浏览器自动化技术
    failed_tasks = await execute_lightweight_download_workflow(
        urls_to_process,  # 要下载的URL列表
        selected_quality,  # 用户选择的音质
        env_manager.temp_dir,  # 临时文件目录
        env_manager.music_dir,  # 最终保存目录
        env_manager.current_browser_data_dir,  # 浏览器数据目录
    )

    # ==================== 第九阶段: 后处理和清理 ====================

    # 处理临时文件，包括清理和恢复
    print("\n" + "=" * 25 + " 🧹 清理与恢复 " + "=" * 25)
    handle_temp_directory_cleanup(env_manager.temp_dir, env_manager.music_dir)

    # 显示性能统计信息
    # 包括下载速度、成功率、总耗时等
    performance_monitor.display_stats()

    # 显示最终下载结果
    # 包括成功数量、失败数量、文件保存位置等
    display_final_results(urls_to_process, failed_tasks, str(env_manager.music_dir))

    # ==================== 第十阶段: 会话结束 ====================

    # 记录会话统计信息到日志
    success_count = len(urls_to_process) - len(failed_tasks)
    logger.info(f"下载会话结束: 成功 {success_count}/{len(urls_to_process)}")

    # 执行程序正常退出的清理工作
    # 包括删除临时文件、关闭浏览器进程等
    env_manager.cleanup_and_exit()

    return 0


if __name__ == "__main__":
    """
    程序入口点
    ===========

    这里处理程序的启动和异常捕获:
    - 使用asyncio.run()启动异步主函数
    - 捕获KeyboardInterrupt（Ctrl+C）进行优雅退出
    - 捕获其他异常并记录错误信息
    - 确保在任何情况下都能正确清理资源
    """
    try:
        # 运行异步主函数并获取退出码
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        # 用户按Ctrl+C中断程序
        print_warn("\n用户中断了程序。正在退出。")
        env_manager.cleanup_and_exit()
        sys.exit(0)
    except Exception as e:
        # 捕获所有未处理的异常
        print_error(f"程序运行时发生未预期的错误: {e}")
        env_manager.cleanup_and_exit()
        sys.exit(1)
